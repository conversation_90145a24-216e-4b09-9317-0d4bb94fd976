<div class="ui-builder">
  <ng-container #outlet></ng-container>
</div>
<ng-template #content>
  <div>
    <ng-content select="[top-screen]"></ng-content>
  </div>
  <mat-card class="cardWithShadow p-10">
    <mat-card-content class="p-15">
      <div class="d-flex justify-content-end m-b-10">
        <!-- <app-breadcrumb *ngIf='!hideBreadCrumb' [forceShow]="true"></app-breadcrumb> -->
        <div class="d-flex flex-row">
          <uib-zone [prefix]="prefix" id="button-list">
            <ng-template uib-template="button" let-config>
              <button
                *ngIf="isShowButton(config?.data.scopes)"
                mat-flat-button
                color="primary"
                type="button"
                class="btn {{ config?.data.class }} btn-md pds-4 m-l-10"
                (click)="onClickButton(config)">
                <div class="d-flex align-items-center">
                  <i-tabler *ngIf="config?.data.icon" class="icon-20 m-r-4 {{ config?.data?.class }}" name="{{ config?.data?.icon }}"></i-tabler>
                  <span>{{ config?.data.title }}</span>
                </div>
              </button>
            </ng-template>
          </uib-zone>
        </div>
      </div>
      <div class="panel">
        <ng-container *ngIf="!hideSearch">
          <div class="d-flex flex-column justify-content-between search-box" *ngIf="!panelOpenState">
            <form class="d-flex flex-start-row flex-row gap-8" [formGroup]="formQuickSearch">
              <ng-container>
                <div class="quick-search-input">
                  <app-text-control
                    [item]="$keyword"
                    [form]="formQuickSearch"
                    (onChanged)="onChangeDebounced($event)"
                    formGroupClass="m-t-0"></app-text-control>
                </div>
                <div [hidden]="isSearchByKeyword || tableConfig?.data?.quickSearchFields?.length === 1" style="min-width: 300px">
                  <app-select-control [item]="$fields" [form]="formQuickSearch"></app-select-control>
                </div>
                <button mat-raised-button color="primary" type="submit" (click)="quickSearch(0)">
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-20 m-r-4" name="search"></i-tabler>
                    <span>Tìm kiếm</span>
                  </div>
                </button>
                <button
                  *ngIf="!tableConfig?.data?.hideSearchAdvanced"
                  mat-raised-button
                  color="basic"
                  type="button"
                  (click)="panelOpenState = !panelOpenState">
                  <div class="d-flex align-items-center">
                    <i-tabler class="icon-20 m-r-4" name="settings-search"></i-tabler>
                    <span>Tìm kiếm nâng cao</span>
                  </div>
                </button>
                <app-dynamic-select-column
                  #dynamicColumnSelector
                  [tableColumn]="tableConfig?.data?.displayedColumns"
                  (onDisplayColumn)="changeDisplayColumn($event)"></app-dynamic-select-column>
              </ng-container>
            </form>
          </div>
          <mat-expansion-panel [@.disabled]="true" class="mat-elevation-z0 pd-0 unset-overflow" style="z-index: 500" [expanded]="panelOpenState">
            <div class="search-advanced-panel">
              <form class="flex-column" [formGroup]="form">
                <uib-zone [prefix]="prefix" id="searchAdvanced" class="search-advanced">
                  <ng-template uib-template="formControl" let-config>
                    <app-form-control-renderer
                      [config]="config"
                      (clickMessageError)="clickMessageError($event)"
                      (formChanged)="formChanged($event)"></app-form-control-renderer>
                  </ng-template>
                </uib-zone>
                <div class="d-flex justify-content-end gap-8">
                  <button mat-raised-button color="basic" type="submit" (click)="resetForm()">
                    <div class="d-flex align-items-center">
                      <i-tabler class="icon-20 m-r-4" name="zoom-reset"></i-tabler>
                    </div>
                  </button>
                  <button mat-raised-button color="primary" type="submit" (click)="searchAdvanced(0)">
                    <div class="d-flex align-items-center">
                      <i-tabler class="icon-20 m-r-4" name="search"></i-tabler>
                      <span>Tìm kiếm</span>
                    </div>
                  </button>
                  <button
                    *ngIf="!tableConfig?.data?.hideSearchAdvanced && !hideButtonClosedAdvancedSearchBox"
                    mat-raised-button
                    color="basic"
                    (click)="panelOpenState = !panelOpenState">
                    <div class="d-flex align-items-center">
                      <i-tabler class="icon-20 m-r-4" name="settings-search"></i-tabler>
                      <span>Tìm kiếm nâng cao</span>
                    </div>
                  </button>
                  <app-dynamic-select-column
                    #dynamicColumnSelector
                    [tableColumn]="tableConfig?.data?.displayedColumns"
                    (onDisplayColumn)="changeDisplayColumn($event)"></app-dynamic-select-column>
                </div>
              </form>
            </div>
          </mat-expansion-panel>
        </ng-container>
        <div class="pd-4">
          <ng-content select="[top-table]"></ng-content>
        </div>
        <uib-zone [prefix]="prefix" id="content">
          <ng-container *uib-template="'table'; let config">
            <app-table-tree
              #appTableBodyComponent
              [configComponent]="config"
              (onTableActionClick)="onTableActionHandler($event)"
              [checkScope]="checkScope"
              (contextMenuClick)="actionContextMenuClick($event)"></app-table-tree>
          </ng-container>
          <ng-container *uib-template="'table-expand'; let config">
            <app-table-expand
              #appTableExpandedComponent
              [configComponent]="config"
              (onTableActionClick)="onTableActionHandler($event)"
              [checkScope]="checkScope"></app-table-expand>
          </ng-container>
        </uib-zone>
      </div>
    </mat-card-content>
  </mat-card>
</ng-template>
