import { Component, Injector } from '@angular/core'
import { ComponentAbstract, MessageSeverity } from '@shared'
import { screenNavigationListConfig } from './screen-navigation-list.config'
import { AppTableBuilderComponent } from './app-table-builder/table-builder.component'
import { ScreenNavigationService } from '../../services/screen-navigation.service'
import { ROUTES_SCREEN_NAVIGATION } from '../../constants'
import { TablerIconsModule } from 'angular-tabler-icons'
import { MatButtonModule } from '@angular/material/button'

@Component({
  selector: 'app-screen-navigation-list',
  templateUrl: './screen-navigation-list.component.html',
  styleUrls: ['./screen-navigation-list.component.scss'],
  standalone: true,
  imports: [AppTableBuilderComponent, TablerIconsModule, MatButtonModule]
})
export class ScreenNavigationListComponent extends ComponentAbstract {
  screenNavigationListConfig = screenNavigationListConfig

  constructor(
    protected override injector: Injector,
    private screenNavigationService: ScreenNavigationService
  ) {
    super(injector)
    this.configService.init(screenNavigationListConfig)
  }

  componentInit(): void {
    // Component initialization logic
  }

  /**
   * Xử lý sự kiện click button trên table
   * @param event Button click event
   */
  onButtonClick(event: any): void {
    switch (event.type) {
      case 'buttonCreate':
        this.navigateToCreate()
        break
      default:
        console.log('Unhandled button click:', event)
    }
  }

  /**
   * Xử lý sự kiện click action trên table row
   * @param event Table action event
   */
  onTableActionClick(event: any): void {
    const { type, row } = event

    switch (type) {
      case 'buttonEdit':
        this.navigateToEdit(row.id)
        break
      case 'buttonDelete':
        this.confirmDelete(row)
        break
      default:
        console.log('Unhandled table action:', event)
    }
  }

  /**
   * Điều hướng đến trang tạo mới
   */
  navigateToCreate(): void {
    this.goTo(ROUTES_SCREEN_NAVIGATION.CREATE)
  }

  /**
   * Điều hướng đến trang chỉnh sửa
   * @param id ID của screen navigation
   */
  navigateToEdit(id: number): void {
    this.goTo(`${ROUTES_SCREEN_NAVIGATION.EDIT}/${id}`)
  }

  /**
   * Hiển thị dialog xác nhận xóa
   * @param data Dữ liệu screen navigation cần xóa
   */
  confirmDelete(data: any): void {
    this.dialogService.confirm(
      {
        title: 'Xác nhận xóa',
        message: `Bạn có chắc chắn muốn xóa cấu hình "${data.configName}" không?`,
        textButtonLeft: 'Hủy',
        textButtonRight: 'Xóa'
      },
      (result) => {
        if (result) {
          this.deleteScreenNavigation(data.id)
        }
      }
    )
  }

  /**
   * Xóa screen navigation
   * @param id ID của screen navigation cần xóa
   */
  private deleteScreenNavigation(id: number): void {
    this.indicator.showActivityIndicator(true)

    this.screenNavigationService.deleteScreenNavigation(id).subscribe({
      next: (response) => {
        this.indicator.showActivityIndicator(false)
        this.toastr.showToastr('Thành công', 'Xóa cấu hình điều hướng thành công', MessageSeverity.success)
        // Refresh table data
        this.refreshTable()
      },
      error: (error) => {
        this.indicator.showActivityIndicator(false)
        this.toastr.showToastr('Lỗi', 'Có lỗi xảy ra khi xóa cấu hình điều hướng', MessageSeverity.error)
        console.error('Delete error:', error)
      }
    })
  }

  /**
   * Refresh table data
   */
  private refreshTable(): void {
    // Trigger table refresh by emitting search event
    // This will be handled by the table component
    window.location.reload()
  }
}
