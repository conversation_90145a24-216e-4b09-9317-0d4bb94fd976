<div class="row">
  <div class="col-12">
    <mat-card class="cardWithShadow">
      <mat-card-content class="p-24">
        <div *ngIf="isLoading" class="text-center p-24">
          <mat-spinner diameter="40"></mat-spinner>
          <p class="m-t-16"><PERSON><PERSON> tải dữ liệu...</p>
        </div>

        <form [formGroup]="form" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
          <div class="row">
            <app-text-control [form]="form" [item]="$formConfigName" class="col-6"></app-text-control>
            <app-text-control [form]="form" [item]="$formButtonName" class="col-6"></app-text-control>
            <app-text-control [form]="form" [item]="$formStackName" class="col-6"></app-text-control>
            <app-text-control [form]="form" [item]="$formScreenName" class="col-6"></app-text-control>
            <app-textarea-control [form]="form" [item]="$formParams" class="col-12"></app-textarea-control>
            <app-select-control [form]="form" [item]="$formStatus" class="col-6"></app-select-control>
          </div>

          <div class="row m-t-24">
            <div class="col-12 d-flex justify-content-center">
              <button 
                mat-stroked-button 
                type="button"
                class="btn btn-md m-r-12"
                (click)="goBack()">                
                Quay lại
              </button>
              <button 
                mat-flat-button 
                color="primary" 
                type="submit"
                class="btn btn-md"
                [disabled]="form.invalid || isSubmitting">
                {{ isSubmitting ? 'Đang cập nhật...' : 'Cập nhật' }}
              </button>
            </div>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  </div>
</div>
