import { Component, Injector } from '@angular/core'
import { ComponentAbstract, MessageSeverity } from '@shared'
import { formConfigName, formButtonName, formStackName, formScreenName, formParams, formStatus } from '../../form/edit.form'
import { UpdateScreenNavigationModel, ScreenNavigationModel } from '../../models/screen-navigation.model'
import { ScreenNavigationService } from '../../services/screen-navigation.service'
import { ROUTES_SCREEN_NAVIGATION } from '../../constants'
import { MatCardModule } from '@angular/material/card'
import { MatButtonModule } from '@angular/material/button'
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'
import { ReactiveFormsModule } from '@angular/forms'
import { CommonModule } from '@angular/common'
import { TablerIconsModule } from 'angular-tabler-icons'
import { TextControlComponent, SelectControlComponent } from '@shared/components/data-input'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'

@Component({
  selector: 'app-screen-navigation-edit',
  templateUrl: './screen-navigation-edit.component.html',
  styleUrls: ['./screen-navigation-edit.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    TablerIconsModule,
    TextControlComponent,
    TextareaControlComponent,
    SelectControlComponent
  ]
})
export class ScreenNavigationEditComponent extends ComponentAbstract {
  $formConfigName = formConfigName()
  $formButtonName = formButtonName()
  $formStackName = formStackName()
  $formScreenName = formScreenName()
  $formParams = formParams()
  $formStatus = formStatus()

  isSubmitting = false
  isLoading = true
  currentScreenNavigation: ScreenNavigationModel | null = null
  screenNavigationId: number

  constructor(
    protected override injector: Injector,
    private screenNavigationService: ScreenNavigationService
  ) {
    super(injector)
  }

  componentInit(): void {
    this.screenNavigationId = Number(this.route.snapshot.paramMap.get('id'))
    if (!this.screenNavigationId) {
      this.toastr.showToastr('Lỗi', 'ID cấu hình điều hướng không hợp lệ', MessageSeverity.error)
      this.goBack()
      return
    }

    this.initForm()
    this.loadScreenNavigationData()
  }

  initForm(): void {
    this.form = this.itemControl.toFormGroup([
      this.$formConfigName,
      this.$formButtonName,
      this.$formStackName,
      this.$formScreenName,
      this.$formParams,
      this.$formStatus
    ])
  }

  loadScreenNavigationData(): void {
    this.isLoading = true

    this.screenNavigationService.getScreenNavigationById(this.screenNavigationId).subscribe({
      next: (response) => {
        this.isLoading = false
        this.currentScreenNavigation = response.data
        this.populateForm()
      },
      error: (error) => {
        this.isLoading = false
        this.showDialogErrorI18n(
          error?.error?.description || 'Có lỗi xảy ra khi tải dữ liệu cấu hình điều hướng',
          'Lỗi khi tải dữ liệu cấu hình điều hướng'
        )
        console.error('Load error:', error)
        this.goBack()
      }
    })
  }

  populateForm(): void {
    if (!this.currentScreenNavigation) return

    this.$formConfigName.value = this.currentScreenNavigation.configName
    this.$formButtonName.value = this.currentScreenNavigation.buttonName
    this.$formStackName.value = this.currentScreenNavigation.stackName
    this.$formScreenName.value = this.currentScreenNavigation.screenName
    this.$formParams.value = this.currentScreenNavigation.params || ''
    this.$formStatus.value = this.currentScreenNavigation.status

    this.form.patchValue({
      configName: this.currentScreenNavigation.configName,
      buttonName: this.currentScreenNavigation.buttonName,
      stackName: this.currentScreenNavigation.stackName,
      screenName: this.currentScreenNavigation.screenName,
      params: this.currentScreenNavigation.params || '',
      status: this.currentScreenNavigation.status
    })
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.itemControl.markUnTouchAllFormGroup(this.form)
      return
    }

    this.isSubmitting = true
    const formValue = this.form.getRawValue()

    // Validate JSON params if provided
    if (formValue.params && formValue.params.trim()) {
      try {
        JSON.parse(formValue.params)
      } catch (error) {
        this.showDialogErrorI18n('Lỗi', 'Tham số phải là JSON hợp lệ')
        this.isSubmitting = false
        return
      }
    }

    const payload: UpdateScreenNavigationModel = {
      configName: formValue.configName,
      buttonName: formValue.buttonName,
      stackName: formValue.stackName,
      screenName: formValue.screenName,
      params: formValue.params || undefined,
      status: formValue.status
    }

    this.screenNavigationService.updateScreenNavigation(this.screenNavigationId, payload).subscribe({
      next: (response) => {
        this.isSubmitting = false
        this.toastr.showToastr('Thành công', 'Cập nhật cấu hình điều hướng thành công', MessageSeverity.success)
        this.goTo(ROUTES_SCREEN_NAVIGATION.LIST)
      },
      error: (error) => {
        this.isSubmitting = false
        const errorMessage = error?.error?.description || 'Có lỗi xảy ra khi cập nhật cấu hình điều hướng'
        this.showDialogErrorI18n('Lỗi', errorMessage)
        console.error('Update error:', error)
      }
    })
  }

  goBack(): void {
    this.goTo(ROUTES_SCREEN_NAVIGATION.LIST)
  }
}
