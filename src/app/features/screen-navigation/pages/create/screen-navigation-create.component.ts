import { Component, Injector } from '@angular/core'
import { ComponentAbstract, MessageSeverity } from '@shared'
import { formConfigName, formButtonName, formStackName, formScreenName, formParams, formStatus } from '../../form/create.form'
import { CreateScreenNavigationModel } from '../../models/screen-navigation.model'
import { ScreenNavigationService } from '../../services/screen-navigation.service'
import { ROUTES_SCREEN_NAVIGATION } from '../../constants'
import { MatCardModule } from '@angular/material/card'
import { MatButtonModule } from '@angular/material/button'
import { ReactiveFormsModule } from '@angular/forms'
import { CommonModule } from '@angular/common'
import { TablerIconsModule } from 'angular-tabler-icons'
import { TextControlComponent, SelectControlComponent } from '@shared/components/data-input'
import { TextareaControlComponent } from '@shared/components/data-input/textarea-control/textarea-control.component'

@Component({
  selector: 'app-screen-navigation-create',
  templateUrl: './screen-navigation-create.component.html',
  styleUrls: ['./screen-navigation-create.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    TablerIconsModule,
    TextControlComponent,
    TextareaControlComponent,
    SelectControlComponent
  ]
})
export class ScreenNavigationCreateComponent extends ComponentAbstract {
  $formConfigName = formConfigName()
  $formButtonName = formButtonName()
  $formStackName = formStackName()
  $formScreenName = formScreenName()
  $formParams = formParams()
  $formStatus = formStatus()

  isSubmitting = false

  constructor(
    protected override injector: Injector,
    private screenNavigationService: ScreenNavigationService
  ) {
    super(injector)
  }

  componentInit(): void {
    this.initForm()
  }

  initForm(): void {
    this.form = this.itemControl.toFormGroup([
      this.$formConfigName,
      this.$formButtonName,
      this.$formStackName,
      this.$formScreenName,
      this.$formParams,
      this.$formStatus
    ])
  }

  onSubmit(): void {
    if (this.form.invalid) {
      this.itemControl.markUnTouchAllFormGroup(this.form)
      return
    }

    this.isSubmitting = true
    const formValue = this.form.getRawValue()

    // Validate JSON params if provided
    if (formValue.params && formValue.params.trim()) {
      try {
        JSON.parse(formValue.params)
      } catch (error) {
        this.showDialogErrorI18n('Lỗi', 'Tham số phải là JSON hợp lệ')
        this.isSubmitting = false
        return
      }
    }

    const payload: CreateScreenNavigationModel = {
      configName: formValue.configName,
      buttonName: formValue.buttonName,
      stackName: formValue.stackName,
      screenName: formValue.screenName,
      params: formValue.params || undefined,
      status: formValue.status
    }

    this.screenNavigationService.createScreenNavigation(payload).subscribe({
      next: (response) => {
        this.isSubmitting = false
        this.showDialogSuccessI18n('', 'Tạo cấu hình điều hướng thành công')
        this.goTo(ROUTES_SCREEN_NAVIGATION.LIST)
      },
      error: (error) => {
        this.isSubmitting = false
        const errorMessage = error?.error?.description || 'Có lỗi xảy ra khi tạo cấu hình điều hướng'
        this.showDialogErrorI18n('Lỗi', errorMessage)
        console.error('Create error:', error)
      }
    })
  }

  goBack(): void {
    this.goTo(ROUTES_SCREEN_NAVIGATION.LIST)
  }
}
