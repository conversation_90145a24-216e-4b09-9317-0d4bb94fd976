import { Injectable } from '@angular/core'

import { Observable } from 'rxjs'
import { environment } from '@env/environment'
import { HttpClientService } from '@shared'

export interface ScreenNavigationModel {
  id: number
  configName: string
  buttonName: string
  stackName: string
  screenName: string
  params: string | null
  status: 'ACTIVE' | 'INACTIVE'
  createdAt: number
  updatedAt: number
  createdBy: string
  updatedBy: string
}

export interface CreateScreenNavigationModel {
  configName: string
  buttonName: string
  stackName: string
  screenName: string
  params?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

export interface UpdateScreenNavigationModel {
  configName?: string
  buttonName?: string
  stackName?: string
  screenName?: string
  params?: string
  status?: 'ACTIVE' | 'INACTIVE'
}

@Injectable({
  providedIn: 'root'
})
export class ScreenNavigationService {
  private readonly baseUrl = `${environment.services.portal}/v1/screen-navigations`

  constructor(private httpClient: HttpClientService) {}

  /**
   * L<PERSON>y danh sách cấu hình screen navigation có phân trang
   * @param params Tham số tìm kiếm
   * @returns Danh sách cấu hình screen navigation có phân trang
   */
  getScreenNavigations(params: any): Observable<any> {
    const options = {
      url: environment.services.portal,
      path: 'v1/screen-navigations'
    }

    if (params) {
      options['params'] = params
    }

    return this.httpClient.get(options)
  }

  /**
   * Lấy thông tin chi tiết cấu hình screen navigation
   * @param id ID cấu hình screen navigation
   * @returns Thông tin chi tiết cấu hình screen navigation
   */
  getScreenNavigationById(id: number): Observable<any> {
    const options = {
      url: environment.services.portal,
      path: `v1/screen-navigations/${id}`
    }

    return this.httpClient.get(options)
  }

  /**
   * Lấy cấu hình screen navigation theo config name
   * @param configName Config name
   * @returns Thông tin cấu hình screen navigation
   */
  getScreenNavigationByConfigName(configName: string): Observable<any> {
    const options = {
      url: environment.services.portal,
      path: `v1/screen-navigations/config/${configName}`
    }

    return this.httpClient.get(options)
  }

  /**
   * Tạo mới cấu hình screen navigation
   * @param params Thông tin cấu hình screen navigation cần tạo
   * @returns Thông tin cấu hình screen navigation đã tạo
   */
  createScreenNavigation(params: CreateScreenNavigationModel): Observable<any> {
    const options = {
      url: environment.services.portal,
      path: 'v1/screen-navigations'
    }

    if (params) {
      options['body'] = params
    }

    return this.httpClient.post(options)
  }

  /**
   * Cập nhật thông tin cấu hình screen navigation
   * @param id ID cấu hình screen navigation
   * @param params Thông tin cấu hình screen navigation cần cập nhật
   * @returns Thông tin cấu hình screen navigation đã cập nhật
   */
  updateScreenNavigation(id: number, params: Partial<UpdateScreenNavigationModel>): Observable<any> {
    const options = {
      url: environment.services.portal,
      path: `v1/screen-navigations/${id}`
    }

    if (params) {
      options['body'] = params
    }

    return this.httpClient.put(options)
  }

  /**
   * Xóa cấu hình screen navigation
   * @param id ID cấu hình screen navigation
   * @returns Kết quả xóa
   */
  deleteScreenNavigation(id: number): Observable<any> {
    const options = {
      url: environment.services.portal,
      path: `v1/screen-navigations/${id}`
    }

    return this.httpClient.delete(options)
  }
}
