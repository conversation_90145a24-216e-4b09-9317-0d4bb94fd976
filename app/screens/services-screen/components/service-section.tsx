import { Alert, Dimensions, FlatList, <PERSON>ing, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext } from 'react'

import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'

import { color, spacing, typography } from '@app/theme'
import { useStores } from '@app/models'
import FastImage from 'react-native-fast-image'
import { observer } from 'mobx-react-lite'
import { InAppBrowser } from 'react-native-inappbrowser-reborn'
import { LazyImage } from '@app/components'
import { useCustomNavigation } from '@app/use-hooks/useNavigation'
import { isJsonString } from '@app/utils'
import { openExternalBrowser } from '@app/utils/browser'

const ServiceSection = observer((props: any) => {
  const { t }: any = useTranslation()
  const { showSuccess } = useContext(ModalContext)
  const { searchStore, profileStore } = useStores()
  const { navigate } = useNavigation<any>()
  const { navigateWithAuth } = useCustomNavigation()

  __DEV__ && console.log('render Categories MucYeuThich', props)

  const sortData = props.data.sort((a, b) => (a.sortOrder ?? 0) - (b.sortOrder ?? 0))

  const openLink = async (link) => {
    if (link) {
      try {
        await InAppBrowser.close()
        await InAppBrowser.open(link, {
          toolbarColor: color.primary,
          dismissButtonStyle: 'close',
          preferredBarTintColor: color.primary,
          preferredControlTintColor: '#fff',
          readerMode: false,
          enableUrlBarHiding: false,
          enableDefaultShare: false,
          animated: true,
          showTitle: false,
          // modalPresentationStyle: 'formSheet',
          modalTransitionStyle: 'coverVertical',
          modalEnabled: false,
          enableBarCollapsing: false,
        })
      } catch (error) {
      }
    }
  }

  const onClickCat = async (item: any) => {
    const type = item?.action?.type
    if (type === 'link') {
      openExternalBrowser(item.action?.target)
      return
    } else {
      const screenName = isJsonString(item.action?.target) ? JSON.parse(item.action?.target)?.screen : item.action?.target

      // Parse target if it's a JSON string, remove screen property
      let params = {}
      if (isJsonString(item.action?.target)) {
        const parsedTarget = JSON.parse(item.action?.target)
        const { screen, ...restParams } = parsedTarget
        params = restParams
      }
    
      const requireActiveAccount = screenName === SCREENS.salaryAdvance
      navigateWithAuth(navigate, screenName, { ...params }, requireActiveAccount)
    }
  }

  const renderIconCategories = ({ item }) => {
    return (
      <TouchableOpacity
        onPress={() => onClickCat(item)}
      >
        <View style={styles.viewDanhMuc}>
          <LazyImage mode='download' style={styles.imageCat} source={{ uri: item.iconPath }} />
          <Text style={styles.itemName}>{item?.name}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  return (<View style={styles.container}>
    {props?.data?.length > 0 ? <View style={{ marginTop: 15 }}>
      <Text style={styles.chucnanguthich}>{props.title}</Text>
      <ScrollView
        horizontal
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <FlatList
          style={{ marginLeft: spacing.small }}
          scrollEnabled={false}
          // contentContainerStyle={{ backgroundColor: color.primaryBackground }}
          showsHorizontalScrollIndicator={true}
          // horizontal={true}
          key={Math.ceil(props.data.length / 2)}
          data={sortData}
          numColumns={3}
          keyExtractor={(_, i) => i.toString()}
          renderItem={renderIconCategories} />
      </ScrollView>
    </View> : null}
  </View>
  )
})

export default ServiceSection

const { width } = Dimensions.get('window')
const styles = StyleSheet.create({

  container: {
    flex: 1,
    margin: 16,
    borderRadius: 15,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },

  imageCat: {
    borderRadius: 25,
    height: 45,
    width: 45
  },
  itemName: {
    color: '#091708',
    fontFamily: typography.normal,
    marginTop: 5,
    width: 80,
    textAlign: 'center',
    fontWeight: '500'
  },

  viewDanhMuc: {
    alignItems: 'center',
    flex: 1,
    height: 100,
    marginBottom: 0,
    paddingVertical: 8,
    width: (width / 3) - 16,
  },

  chucnanguthich: {
    fontSize: 16,
    fontWeight: '500',
    color: '#091708',
    marginLeft: 16,
    marginBottom: 10
  },
  xemtatca: {
    fontSize: 14,
    fontWeight: '500',
    color: '#002845',
    textAlign: 'left',
    textAlignVertical: 'top'
  },
  frame1000002804: {
    marginTop: 20,
    marginHorizontal: 18,
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
})
