import { Alert, Dimensions, FlatList, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useContext, useEffect } from 'react'
import orderBy from 'lodash/orderBy'

import { ModalContext } from '@app/context'
import { SCREENS } from '@app/navigation'
import { useCustomNavigation } from '@app/use-hooks/useNavigation'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'

import { color, spacing, typography } from '@app/theme'
import { useStores } from '@app/models'
import { observer } from 'mobx-react-lite'
import { LazyImage } from '@app/components'
import { isJsonString } from '@app/utils'
import { openExternalBrowser } from '@app/utils/browser'

const FavoriteFeatures = observer((props: any) => {
  const { t }: any = useTranslation()
  const { showSuccess, showError } = useContext(ModalContext)
  const { homeStore, profileStore } = useStores()
  const { navigate } = useNavigation<any>()
  const { navigateWithAuth } = useCustomNavigation()

  __DEV__ && console.log('render Categories MucYeuThich', props)
  
  // Load categories on component mount
  // useEffect(() => {
  //   homeStore.getListCategories()
  // }, [])

  // Transform categories for display
  const transformCategories = () => {
    if (!homeStore.listCategories || homeStore.listCategories.length === 0) {
      return []
    }

    // Map and include sortOrder, then sort
    const mapped = homeStore.listCategories
    return orderBy(mapped, ['sortOrder'], ['asc'])
  }


  const onClickCat = async (item: any) => {

    const type = item?.action?.type
    if (type === 'link') {
      openExternalBrowser(item.action?.target)
      return
    } else {
      const screenName = isJsonString(item.action?.target) ? JSON.parse(item.action?.target)?.screen : item.action?.target

      // Parse target if it's a JSON string, remove screen property
      let params = {}
      if (isJsonString(item.action?.target)) {
        const parsedTarget = JSON.parse(item.action?.target)
        const { screen, ...restParams } = parsedTarget
        params = restParams
      }
    
      const requireActiveAccount = screenName === SCREENS.salaryAdvance
      navigateWithAuth(navigate, screenName, { ...params }, requireActiveAccount)   
    }
  
  }
  const onPressXemTatCa = async () => {
    navigateWithAuth(navigate, SCREENS.servicesScreen)
  }

  const renderIconCategories = ({ item }) => {
    __DEV__ && console.log('renderIconCategories item:', item)
    return (
      <TouchableOpacity
        onPress={() => onClickCat(item)}
        style={styles.touchableContainer}
      >
        <View style={styles.viewDanhMuc}>
          <LazyImage mode="download" resizeMode="cover" style={styles.imageCat} source={{ uri: item.iconPath }} />
          <Text style={styles.itemName}>{item?.name}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  // Get categories data from homeStore or props
  const categoriesData = homeStore.listCategories?.length > 0 ? transformCategories() : props?.data || []

  return (<View>
    {categoriesData?.length > 0 ? <View style={{ marginTop: 15 }}>
      <View style={styles.frame1000002804}>
        <Text style={styles.chucnanguthich}>Chức năng ưa thích</Text>
        <TouchableOpacity onPress={() => onPressXemTatCa()}>
          <Text style={styles.xemtatca}>Xem tất cả</Text>
        </TouchableOpacity>
      </View>
      <FlatList
          style={{ marginLeft: spacing.small }}
          scrollEnabled={false}
          contentContainerStyle={{ backgroundColor: color.primaryBackground, justifyContent: 'space-between', alignItems: 'center' }}
          showsHorizontalScrollIndicator={true}
          data={categoriesData}
          numColumns={3}
          keyExtractor={(e, i) => (i + 1).toString()}
          renderItem={renderIconCategories} />
    </View> : null}
  </View>
  )
})

export default FavoriteFeatures

const { width } = Dimensions.get('window')
const styles = StyleSheet.create({
  touchableContainer: {
    width: (width / 3) - 20,
  },
  imageCat: {
    height: 45,
    width: 45,
    borderRadius: 15,
    backgroundColor: '#fff'
  },
  itemName: {
    color: '#091708',
    fontFamily: typography.normal,
    marginTop: 5,
    textAlign: 'center',
    fontWeight: '500',
    flexWrap: 'wrap',
  },
  viewDanhMuc: {
    alignItems: 'center',
    height: 100,
    marginBottom: 0,
    paddingVertical: 8,
  },
  chucnanguthich: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0a2540',
    textAlign: 'left',
    textAlignVertical: 'top'
  },
  xemtatca: {
    fontSize: 14,
    fontWeight: '500',
    color: '#002845',
    textAlign: 'left',
    textAlignVertical: 'top'
  },
  frame1000002804: {
    marginTop: 20,
    marginHorizontal: 18,
    marginBottom: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
})
