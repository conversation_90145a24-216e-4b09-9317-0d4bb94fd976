import React, { useEffect } from 'react'
import { observer } from 'mobx-react-lite'
import { View, Text, ScrollView, useWindowDimensions, TouchableOpacity, Alert } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useStores } from '@app/models'
import RenderHtml from 'react-native-render-html'
import { ButtonBack, LazyImage, PlaceHolder } from '@app/components'
import styles from './styles'

import common from '@app/theme/styles/common'
import { Header } from 'react-native-elements'
import { EVENT } from '@app/constants/event'

const renderersProps = {
  img: {
    enableExperimentalPercentWidth: true
  }
}

export const NewsDetailScreen = observer(function NewsDetailScreen() {
  const route: any = useRoute()
  const { id } = route?.params
  const { newsStore, pubSubStore } = useStores()
  const { goBack } = useNavigation<any>()
  const { width } = useWindowDimensions()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      await newsStore.getNewsDetail(id)
    } catch (error) {
      __DEV__ && console.error('Error loading news detail:', error)
    }
  }

  const handleNavigationPress = () => {
    const screenNavigation = newsStore.currentNews?.screenNavigation
    if (!screenNavigation) return

    try {
      // Parse params if exists
      let params: any = null
      if (screenNavigation.params) {
        try {
          params = JSON.parse(screenNavigation.params)
        } catch (parseError) {
          __DEV__ && console.warn('Failed to parse navigation params:', parseError)
          // Nếu parse JSON thất bại, sử dụng params như string
          params = screenNavigation.params
        }
      }

      // Tạo navigation params theo format chuẩn
      const navigationParams = {
        stack: screenNavigation.stackName,
        screen: screenNavigation.screenName,
        params: params
      }

      __DEV__ && console.log('🚀 NEWS NAVIGATION - Publishing event with params:', JSON.stringify(navigationParams, null, 2))
      pubSubStore?.publish?.(EVENT.NAVIGATION, navigationParams)

    } catch (error) {
      __DEV__ && console.error('Navigation error:', error)
      Alert.alert('Lỗi', 'Không thể điều hướng đến màn hình được chỉ định')
    }
  }

  return (
    <View style={styles.container}>
      <Header
        leftComponent={<ButtonBack style={common.buttonBack} onPress={goBack} />}
        centerComponent={{ text: newsStore.currentNews?.title || '', style: common.headerCenterTitle }}
        containerStyle={common.headerContainer}
        statusBarProps={{ barStyle: 'light-content' }}
      />
      <ScrollView showsVerticalScrollIndicator={false} showsHorizontalScrollIndicator={false}>
        {newsStore.isLoading ? <PlaceHolder/>
          : newsStore.currentNews ? <View>
            <Text style={styles.newsTitle}>{newsStore.currentNews.title}</Text>
            <LazyImage mode='download' style={styles.newsPicture} source={{ uri: newsStore.currentNews.thumbnailUrl || '' }}></LazyImage>
            <View style={{ flexGrow: 0, backgroundColor: '#fff', paddingBottom: 15, paddingHorizontal: 16 }}>
              <RenderHtml
                contentWidth={width}
                source={{ html: newsStore.currentNews?.content || '' }}
                renderersProps={renderersProps}
                ignoredDomTags={['script']}
              />

              {/* Navigation Button */}
              {newsStore.currentNews?.screenNavigation && (
                <TouchableOpacity
                  style={styles.navigationButton}
                  onPress={handleNavigationPress}
                >
                  <Text style={styles.navigationButtonText}>
                    {newsStore.currentNews.screenNavigation.buttonName}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          : <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 40 }}>
              <Text style={{ fontSize: 16, color: '#666' }}>Không tìm thấy tin tức</Text>
            </View>
        }
      </ScrollView>
    </View>
  )
})
